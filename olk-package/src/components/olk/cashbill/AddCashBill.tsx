"use client";
import React, { useState, useRef, useEffect, useCallback } from "react";
import {
    useForm,
    FormProvider,
    Controller,
    useFieldArray,
    useFormContext,
} from "react-hook-form";
import { Card } from "primereact/card";
import { InputText } from "primereact/inputtext";
import { Calendar } from "primereact/calendar";
import { Button } from "primereact/button";
import { Toast } from "primereact/toast";
import { Divider } from "primereact/divider";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { Dropdown } from "primereact/dropdown";
import { InputNumber } from "primereact/inputnumber";
import { Checkbox } from "primereact/checkbox";
import { useSearchParams } from "next/navigation";
import "../../../styles/olkcss.scss";
import { getOrgDataField } from "../../../utils/cookies";
import axiosInstance from "../../../utils/axios";
import { useEnvContext } from "../../../contexts/EnvContextProvider";
import {
    CashBillFormValues,
    CashBillItem,
    cashBillNumberSchema,
} from "../../../schema/cashbill";
import { useRouter } from "next/navigation";
import { OlkProvider } from "../../../contexts/OlkProvider";
import { ProgressSpinner } from "primereact/progressspinner";
import { RadioButton } from "primereact/radiobutton";
import apiCall from "../../../utils/apiCallService";

//   AddCashBill Component

//   This component provides a form for creating cash bills (cash memos) under GST.
//   Cash bills are issued when buyers settle consideration immediately in cash or equivalent modes.

//   Key features:
//   - Consecutive alphanumeric series for cash bill numbers
//   - Immediate payment settlement (no credit option)
//   - Simplified validation for cash transactions
//   - Auto-calculation of GST and totals
// Add this utility function at the top level

const numberToWords = (num: number): string => {
    const ones = [
        "",
        "One",
        "Two",
        "Three",
        "Four",
        "Five",
        "Six",
        "Seven",
        "Eight",
        "Nine",
    ];
    const tens = [
        "",
        "",
        "Twenty",
        "Thirty",
        "Forty",
        "Fifty",
        "Sixty",
        "Seventy",
        "Eighty",
        "Ninety",
    ];
    const teens = [
        "Ten",
        "Eleven",
        "Twelve",
        "Thirteen",
        "Fourteen",
        "Fifteen",
        "Sixteen",
        "Seventeen",
        "Eighteen",
        "Nineteen",
    ];

    const convertLessThanThousand = (n: number): string => {
        if (n === 0) return "";
        if (n < 10) return ones[n];
        if (n < 20) return teens[n - 10];
        if (n < 100)
            return (
                tens[Math.floor(n / 10)] +
                (n % 10 !== 0 ? " " + ones[n % 10] : "")
            );
        return (
            ones[Math.floor(n / 100)] +
            " Hundred" +
            (n % 100 !== 0 ? " and " + convertLessThanThousand(n % 100) : "")
        );
    };

    const convert = (n: number): string => {
        if (n === 0) return "Zero";
        if (n < 1000) return convertLessThanThousand(n);
        if (n < 100000)
            return (
                convertLessThanThousand(Math.floor(n / 1000)) +
                " Thousand" +
                (n % 1000 !== 0 ? " " + convertLessThanThousand(n % 1000) : "")
            );
        if (n < 10000000)
            return (
                convertLessThanThousand(Math.floor(n / 100000)) +
                " Lakh" +
                (n % 100000 !== 0 ? " " + convert(Math.floor(n % 100000)) : "")
            );
        return (
            convertLessThanThousand(Math.floor(n / 10000000)) +
            " Crore" +
            (n % 10000000 !== 0 ? " " + convert(Math.floor(n % 10000000)) : "")
        );
    };

    const rupees = Math.floor(num);
    const paise = Math.round((num - rupees) * 100);
    let result = convert(rupees) + " Rupees";
    if (paise > 0) {
        result += " and " + convert(paise) + " Paise";
    }
    return result + " Only";
};

const formatIndianCurrency = (amount: number | string): string => {
    const num = typeof amount === "string" ? parseFloat(amount) : amount;
    if (isNaN(num)) return "₹0.00";

    // Convert to fixed 2 decimal places
    const fixedNum = num.toFixed(2);
    const [integerPart, decimalPart] = fixedNum.split(".");

    // Apply Indian number formatting (lakhs and crores)
    const lastThreeDigits = integerPart.slice(-3);
    const otherDigits = integerPart.slice(0, -3);

    if (otherDigits !== "") {
        const formattedOtherDigits = otherDigits.replace(
            /\B(?=(\d{2})+(?!\d))/g,
            ","
        );
        return `${formattedOtherDigits},${lastThreeDigits}.${decimalPart}`;
    } else {
        return `${lastThreeDigits}.${decimalPart}`;
    }
};

const CashBillForm: React.FC = () => {
    const { OLK_PATH } = useEnvContext();
    const searchParams = useSearchParams();
    const initialType = searchParams.get("type") || "sales";
    const [cashBillType, setCashBillType] = useState<"sales" | "purchase">(
        initialType as "sales" | "purchase"
    );
    const title =
        cashBillType === "sales" ? "Cash Bill - Sales" : "Cash Bill - Purchase";
    const toast = useRef<Toast>(null);
    const orgcode = Number(getOrgDataField("orgcode"));
    const orgstate = getOrgDataField("orgstate");
    const isoString1 = getOrgDataField("yearstart");
    const yearstart = isoString1
        ? new Date(isoString1).toISOString().split("T")[0]
        : "";
    const isoString2 = getOrgDataField("yearend");
    const yearend = isoString2
        ? new Date(isoString2).toISOString().split("T")[0]
        : "";

    const router = useRouter();

    // Form setup with default values and real-time validation
    const methods = useForm<
        CashBillFormValues & { cashBillItems: CashBillItem[] }
    >({
        mode: "onChange", // Enable real-time validation
        defaultValues: {
            cashBillNo: "",
            cashBillDate: new Date(),
            paymentMethod: "Cash",
            narration: "",
            cashBillItems: [
                {
                    id: Date.now(),
                    product: null,
                    hsn: "",
                    qty: 0.0,
                    freeQty: 0.0,
                    amount: 0.0,
                    discount: 0.0,
                    taxableValue: 0.0,
                    gstRate: 0.0,
                    gstAmount: 0.0,
                    total: 0.0,
                },
            ],
        },
    });

    const {
        control,
        handleSubmit,
        watch,
        setValue,
        getValues,
        reset,
        trigger,
        formState: { errors },
    } = methods;

    const {
        fields: cashBillItemsFields,
        append,
        remove,
    } = useFieldArray({
        control,
        name: "cashBillItems",
    });

    interface InvoiceResponse {
        success: number;
        invid?: number;
        message?: string;
    }

    interface ApiResponse {
        olkstatus: number;
        olkresult: [];
    }

    interface BisDetails {
        bankdetails: any;
        gstin: string;
        orgpan: string;
        orgaddr: string;
        orgpincode: string;
    }

    interface OrgBdtResponse {
        olkstatus: number;
        bisdetails: BisDetails;
    }

    interface ApiCashBill {
        invid: number;
        invoiceno: string;
        invoicedate: string;
        invoicetotal: string;
        nettotal: string;
        amountpaid: string;
    }
    interface ApiCashBills {
        olkstatus: number;
        invoices: ApiCashBill[];
    }

    // Watch form values for calculations
    const watchedItems = watch("cashBillItems");
    const paymentMethod = watch("paymentMethod");

    // State for products and calculations
    const [products, setProducts] = useState<any[]>([]);
    const [productLoading, setProductLoading] = useState(false);
    const [loading, setLoading] = useState(false);
    const [cashBillNoValidating, setCashBillNoValidating] = useState(false);
    const [hasHsnOverridePermission, setHasHsnOverridePermission] =
        useState(false);
    const [discountType, setDiscountType] = useState<"amount" | "percentage">(
        "amount"
    );
    const [totals, setTotals] = useState({
        totalTaxable: 0,
        totalGST: 0,
        totalDiscount: 0,
        grandTotal: 0,
    });
    const [amountInWords, setAmountInWords] = useState("");
    const [orgGstin, setOrgGstin] = useState<string | null>(null);
    const [applyGst, setApplyGst] = useState<boolean>(true); // For purchase cash bills

    // Payment options for cash bills (no credit)
    const paymentOptions = [
        { label: "Cash", value: "Cash" },
        { label: "Bank", value: "Bank" },
        { label: "Online", value: "Online" },
    ];

    const paymentModeMap: Record<string, 2 | 3> = {
        Cash: 3,
        Bank: 2,
        Online: 2,
    };

    //Fetch products/services from API

    useEffect(() => {
        const fetchProducts = async () => {
            setProductLoading(true);
            try {
                const response = await apiCall<ApiResponse>(
                    "GET",
                    `${OLK_PATH}/products?orgcode=${orgcode}`
                );

                //console.log("Product Response:", response.data);
                if (response.data.olkstatus === 1) {
                    toast.current?.show({
                        severity: "error",
                        summary: "Error",
                        detail: "Something went wrong",
                    });
                } else if (
                    response.data.olkstatus === 0 &&
                    response.data.olkresult.length === 0
                ) {
                    toast.current?.show({
                        severity: "info",
                        summary: "No Data",
                        detail: "No products found",
                    });
                } else {
                    setProducts(response.data.olkresult);
                }
            } catch (error) {
                console.error("Error fetching products:", error);
                toast.current?.show({
                    severity: "error",
                    summary: "API Error",
                    detail: "Failed to fetch products",
                });
            } finally {
                setProductLoading(false);
            }
        };

        if (orgcode && OLK_PATH) {
            fetchProducts();
        }
    }, [orgcode, OLK_PATH]);

    // Fetch organization data to get GSTIN
    useEffect(() => {
        const fetchOrgData = async () => {
            try {
                const response = await apiCall<OrgBdtResponse>(
                    "GET",
                    `${OLK_PATH}/organisations/bdt?orgcode=${orgcode}`
                );
                const { olkstatus, bisdetails } = response.data;
                if (olkstatus === 0) {
                    setOrgGstin(bisdetails.gstin || null);
                }
            } catch (error) {
                console.error("Error fetching organization data:", error);
                setOrgGstin(null);
            }
        };

        if (orgcode && OLK_PATH) {
            fetchOrgData();
        }
    }, [orgcode, OLK_PATH]);

    // Check HSN override permission
    useEffect(() => {
        const checkHsnPermission = async () => {
            try {
                setHasHsnOverridePermission(true);
            } catch (error) {
                console.error("Error checking HSN permission:", error);
                setHasHsnOverridePermission(false);
            }
        };

        if (orgcode && OLK_PATH) {
            checkHsnPermission();
        }
    }, [orgcode, OLK_PATH]);

    // Real-time calculation function

    const calculateTotals = useCallback(() => {
        const items = getValues("cashBillItems");
        let totalTaxable = 0;
        let totalGST = 0;
        let totalDiscount = 0;
        let grandTotal = 0;

        items.forEach((item, index) => {
            // Ensure we have valid numbers
            const amount = Number(item.amount) || 0;
            const qty = Number(item.qty) || 0;
            const discountValue = Number(item.discount) || 0;
            const gstRate = Number(item.gstRate) || 0;

            // Calculate base value: qty * amount
            const baseValue = qty * amount;

            // Calculate actual discount amount based on global discount type
            let actualDiscountAmount = 0;

            if (discountType === "percentage") {
                // Calculate percentage discount on base value
                actualDiscountAmount = (baseValue * discountValue) / 100;
            } else {
                // Direct amount discount
                actualDiscountAmount = discountValue;
            }

            // Calculate taxable value: (qty * amount) - discount
            const taxableValue = Math.max(0, baseValue - actualDiscountAmount);

            // Apply GST validation rules
            let effectiveGstRate = gstRate;

            if (cashBillType === "sales") {
                // For sales: if GSTIN is null, GST should be 0
                if (!orgGstin) {
                    effectiveGstRate = 0;
                }
            } else {
                // For purchase: if applyGst checkbox is unchecked, GST should be 0
                if (!applyGst) {
                    effectiveGstRate = 0;
                }
            }

            // Calculate GST amount with effective rate
            const gstAmount = (taxableValue * effectiveGstRate) / 100;

            // Calculate total for this item
            const itemTotal = taxableValue + gstAmount;

            // Update item values in form - this triggers real-time updates
            setValue(
                `cashBillItems.${index}.taxableValue`,
                Number(taxableValue.toFixed(2)),
                { shouldValidate: false, shouldDirty: false }
            );
            setValue(
                `cashBillItems.${index}.gstAmount`,
                Number(gstAmount.toFixed(2)),
                { shouldValidate: false, shouldDirty: false }
            );
            setValue(
                `cashBillItems.${index}.total`,
                Number(itemTotal.toFixed(2)),
                { shouldValidate: false, shouldDirty: false }
            );

            // Add to totals
            totalTaxable += taxableValue;
            totalGST += gstAmount;
            totalDiscount += actualDiscountAmount;
            grandTotal += itemTotal;
        });

        // Update totals state - this will trigger cash bill summary updates
        const newTotals = {
            totalTaxable: Number(totalTaxable.toFixed(2)),
            totalGST: Number(totalGST.toFixed(2)),
            totalDiscount: Number(totalDiscount.toFixed(2)),
            grandTotal: Number(grandTotal.toFixed(2)),
        };

        setTotals(newTotals);

        // console.log("Real-time calculation update:", newTotals);
        // console.log("Current items:", items);
    }, [getValues, setValue, discountType, cashBillType, orgGstin, applyGst]);

    // Calculate totals whenever items change - Real-time calculations
    useEffect(() => {
        calculateTotals();
    }, [watchedItems, calculateTotals]);

    // Add this effect to update amount in words
    useEffect(() => {
        setAmountInWords(numberToWords(totals.grandTotal));
    }, [totals.grandTotal]);

    // Debounce timer for real-time validation
    const validationTimeoutRef = useRef<NodeJS.Timeout | null>(null);

    // Validate cash bill number using Zod schema
    const validateCashBillNumberFormat = (cashBillNo: string) => {
        try {
            cashBillNumberSchema.parse(cashBillNo);
            return true;
        } catch (error: any) {
            return (
                error.errors?.[0]?.message || "Invalid cash bill number format"
            );
        }
    };

    // Real-time input filter for cash bill number
    const filterCashBillNumberInput = (
        input: string,
        currentValue: string = ""
    ): string => {
        // Remove any characters that are not allowed: A-Z, a-z, 0-9, -, /
        let filtered = input.replace(/[^A-Za-z0-9\-\/]/g, "");

        // Limit to 16 characters maximum
        if (filtered.length > 16) {
            filtered = filtered.substring(0, 16);
        }

        // If this is the first character, ensure it's not 0, -, or /
        if (filtered.length === 1 && /^[0\-\/]/.test(filtered)) {
            return currentValue; // Return the previous value, don't allow the invalid first character
        }

        // For subsequent characters after the first, check if the first character would be invalid
        if (filtered.length > 0 && /^[0\-\/]/.test(filtered.charAt(0))) {
            // If the first character is invalid, remove it
            filtered = filtered.substring(1);
        }

        return filtered;
    };

    // Handle key down events to prevent invalid characters
    const handleCashBillNumberKeyDown = (
        e: React.KeyboardEvent<HTMLInputElement>
    ) => {
        const char = e.key;
        const currentValue = (e.target as HTMLInputElement).value;

        // Allow control keys (backspace, delete, arrow keys, etc.)
        if (e.ctrlKey || e.metaKey || char.length > 1) {
            return;
        }

        // Check if the character is allowed
        if (!/[A-Za-z0-9\-\/]/.test(char)) {
            e.preventDefault();
            return;
        }

        // Check if adding this character would exceed 16 characters
        if (currentValue.length >= 16) {
            e.preventDefault();
            return;
        }

        // If this would be the first character, ensure it's not 0, -, or /
        if (currentValue.length === 0 && /[0\-\/]/.test(char)) {
            e.preventDefault();
            return;
        }
    };

    // Validate cash bill number uniqueness
    const validateCashBillNumber = async (cashBillNo: string) => {
        if (!cashBillNo || cashBillNo.length < 1) return true;

        setCashBillNoValidating(true);
        try {
            const response = await apiCall<ApiCashBills>(
                "GET",
                `${OLK_PATH}/invoice/getbills` +
                    `?orgcode=${encodeURIComponent(orgcode)}` +
                    `&inoutflag=${cashBillType === "sales" ? 15 : 9}`
            );

            if (response.data?.olkstatus === 0 && response.data?.invoices) {
                // Check if any existing cash bill has the same number
                const existingCashBill = response.data.invoices.find(
                    (invoice: any) => invoice.invoiceno === cashBillNo
                );

                if (existingCashBill) {
                    return "Cash bill number already exists in the current financial year";
                }
            }
            return true;
        } catch (error) {
            console.error("Error validating cash bill number:", error);
            return true; // Allow submission if validation fails
        } finally {
            setCashBillNoValidating(false);
        }
    };

    // Debounced validation function for real-time validation
    const debouncedValidateCashBillNumber = useCallback(
        (cashBillNo: string) => {
            // Clear existing timeout
            if (validationTimeoutRef.current) {
                clearTimeout(validationTimeoutRef.current);
            }

            // Set new timeout for debounced validation
            validationTimeoutRef.current = setTimeout(() => {
                validateCashBillNumber(cashBillNo);
            }, 500); // 500ms delay to avoid excessive API calls
        },
        [validateCashBillNumber]
    );

    // Cleanup timeout on unmount
    useEffect(() => {
        return () => {
            if (validationTimeoutRef.current) {
                clearTimeout(validationTimeoutRef.current);
            }
        };
    }, []);

    // Add new item row
    const addItem = () => {
        const newItem: CashBillItem = {
            id: Date.now(),
            product: null,
            hsn: "",
            qty: 0.0,
            freeQty: 0.0,
            amount: 0.0,
            discount: 0.0,
            taxableValue: 0.0,
            gstRate: 0.0,
            gstAmount: 0.0,
            total: 0.0,
        };
        append(newItem);

        // Trigger recalculation after adding item
        setTimeout(() => {
            calculateTotals();
        }, 0);
    };

    // Remove item row

    const removeItem = (index: number) => {
        if (cashBillItemsFields.length > 1) {
            remove(index);

            // Trigger recalculation after removing item
            setTimeout(() => {
                calculateTotals();
            }, 0);
        }
    };

    // Handle form submission

    const onSubmit = async (
        data: CashBillFormValues & { cashBillItems: CashBillItem[] }
    ) => {
        setLoading(true);

        try {
            // Validate required fields for each item using the same logic as AddInvoice
            const hasInvalidItems = data.cashBillItems.some((item, index) => {
                if (!item.product) {
                    toast.current?.show({
                        severity: "error",
                        summary: "Validation Error",
                        detail: `Product is required for item ${index + 1}`,
                    });
                    return true;
                }

                // Validate amount
                if (!item.amount || item.amount <= 0) {
                    toast.current?.show({
                        severity: "error",
                        summary: "Validation Error",
                        detail: `Amount must be a positive number with up to 3 decimal places for item ${
                            index + 1
                        }`,
                    });
                    return true;
                } else if (!/^\d*\.?\d{0,3}$/.test(item.amount.toString())) {
                    toast.current?.show({
                        severity: "error",
                        summary: "Validation Error",
                        detail: `Amount must have up to 3 decimal places for item ${
                            index + 1
                        }`,
                    });
                    return true;
                }

                // Validate quantity for products (gsflag === 7)
                if (
                    item.product?.gsflag === 7 &&
                    (!item.qty || item.qty <= 0)
                ) {
                    toast.current?.show({
                        severity: "error",
                        summary: "Validation Error",
                        detail: `Quantity must be a positive number with up to 3 decimal places for item ${
                            index + 1
                        }`,
                    });
                    return true;
                } else if (
                    item.product?.gsflag === 7 &&
                    !/^\d*\.?\d{0,3}$/.test(item.qty.toString())
                ) {
                    toast.current?.show({
                        severity: "error",
                        summary: "Validation Error",
                        detail: `Quantity must have up to 3 decimal places for item ${
                            index + 1
                        }`,
                    });
                    return true;
                }

                // Validate free quantity
                if (item.freeQty < 0) {
                    toast.current?.show({
                        severity: "error",
                        summary: "Validation Error",
                        detail: `Free Quantity must be non-negative with up to 3 decimal places for item ${
                            index + 1
                        }`,
                    });
                    return true;
                } else if (!/^\d*\.?\d{0,3}$/.test(item.freeQty.toString())) {
                    toast.current?.show({
                        severity: "error",
                        summary: "Validation Error",
                        detail: `Free Quantity must have up to 3 decimal places for item ${
                            index + 1
                        }`,
                    });
                    return true;
                }

                // Validate discount
                if (discountType === "percentage" && item.discount > 100) {
                    toast.current?.show({
                        severity: "error",
                        summary: "Validation Error",
                        detail: `Percentage discount cannot exceed 100% for item ${
                            index + 1
                        }`,
                    });
                    return true;
                } else if (
                    discountType === "percentage" &&
                    !/^\d*\.?\d{0,2}$/.test(item.discount.toString())
                ) {
                    toast.current?.show({
                        severity: "error",
                        summary: "Validation Error",
                        detail: `Percentage discount must have up to 2 decimal places for item ${
                            index + 1
                        }`,
                    });
                    return true;
                } else if (
                    discountType === "amount" &&
                    !/^\d*\.?\d{0,3}$/.test(item.discount.toString())
                ) {
                    toast.current?.show({
                        severity: "error",
                        summary: "Validation Error",
                        detail: `Amount discount must have up to 3 decimal places for item ${
                            index + 1
                        }`,
                    });
                    return true;
                }

                return false;
            });

            if (hasInvalidItems) {
                setLoading(false);
                return;
            }

            // Validate amount in words
            const calculatedAmountInWords = numberToWords(totals.grandTotal);
            if (calculatedAmountInWords !== amountInWords) {
                toast.current?.show({
                    severity: "error",
                    summary: "Validation Error",
                    detail: "Amount in words does not match the total amount",
                });
                setLoading(false);
                return;
            }

            // Validate rounding difference
            const roundedTotal = Math.round(totals.grandTotal);
            const difference = Math.abs(roundedTotal - totals.grandTotal);
            if (difference > 1) {
                toast.current?.show({
                    severity: "error",
                    summary: "Validation Error",
                    detail: "Total amount difference is more than 1",
                });
                setLoading(false);
                return;
            }

            // Use current totals from state

            // Prepare payload according to API specification
            const payload = {
                icflag: 3, // Cash bill flag
                inoutflag: cashBillType === "sales" ? 15 : 9, // 15 = Sales, 9 = Purchase
                invoiceno: data.cashBillNo,
                invoicedate: data.cashBillDate.toISOString().split("T")[0],
                taxstate: orgstate || "",
                sourcestate: orgstate || "",
                contents: data.cashBillItems.map((item) => {
                    // Apply GST validation rules for each item
                    let effectiveGstRate = item.gstRate || 0;

                    if (cashBillType === "sales") {
                        // For sales: if GSTIN is null, GST should be 0
                        if (!orgGstin) {
                            effectiveGstRate = 0;
                        }
                    } else {
                        // For purchase: if applyGst checkbox is unchecked, GST should be 0
                        if (!applyGst) {
                            effectiveGstRate = 0;
                        }
                    }

                    // Calculate discount amount based on type
                    let discountAmount = item.discount || 0;
                    if (discountType === "percentage") {
                        const baseValue = (item.qty || 0) * (item.amount || 0);
                        discountAmount = (baseValue * discountAmount) / 100;
                    }

                    // Calculate taxable amount
                    const baseValue = (item.qty || 0) * (item.amount || 0);
                    const taxableAmount = Math.max(
                        0,
                        baseValue - discountAmount
                    );

                    // Calculate GST amount
                    const gstAmount = (taxableAmount * effectiveGstRate) / 100;

                    // Calculate product amount (total for this item)
                    const productAmount = taxableAmount + gstAmount;

                    return {
                        productcode: item.product?.productcode || 0,
                        productname: item.product?.productdesc || "",
                        quantity: item.qty || 0,
                        freeQuantity: item.freeQty || 0,
                        gstflag: item.product?.gstflag || 0,
                        gstrate: effectiveGstRate,
                        pricePerUnit: item.amount || 0,
                        gstamount: gstAmount,
                        taxableAmount: taxableAmount,
                        productAmount: productAmount,
                        gsflag: item.product?.gsflag || 0,
                        discountAmount: discountAmount,
                    };
                }),
                roundoffflag: 0,
                invoicetotal: totals.grandTotal,
                nettotal: totals.totalTaxable,
                invoicetotalword: amountInWords,
                amountpaid: totals.grandTotal, // For cash bills, amount paid = invoice total
                orgcode,
                paymentmode: paymentModeMap[data.paymentMethod],
                discflag: discountType === "percentage" ? 16 : 1,
                invnarration: data.narration || "",
            };

            const response = await apiCall<InvoiceResponse>(
                "POST",
                `${OLK_PATH}/invoice`,
                payload
            );

            if (response.data?.success === 1) {
                toast.current?.show({
                    severity: "success",
                    summary: "Success",
                    detail: "Cash bill created successfully.",
                });

                // Reset form
                reset();

                // Navigate to view if ID is returned
                if (response.data?.invid) {
                    router.push(
                        `/dashboard/invoice/cashbill/view/${response.data.invid}`
                    );
                }
            }
        } catch (error) {
            console.error("Error creating cash bill:", error);
            toast.current?.show({
                severity: "error",
                summary: "Error",
                detail: "Failed to create cash bill",
            });
        } finally {
            setLoading(false);
        }
    };

    const handleViewCashBills = () => {
        router.push(
            `/dashboard/invoice/cashbill/list?type=${cashBillType}&title=${encodeURIComponent(
                title
            )}`
        );
    };

    // Handle cash bill type change
    const handleCashBillTypeChange = (type: "sales" | "purchase") => {
        // console.log("Cash Bill Type Changing from:", cashBillType, "to:", type);
        setCashBillType(type);
        // Reset form when type changes
        // console.log("Resetting form...");
        reset();
        // Update URL without page reload
        const newUrl = `/dashboard/invoice/cashbill?type=${type}`;
        // console.log("Updating URL to:", newUrl);
        window.history.pushState({}, "", newUrl);
    };

    // Add effect to monitor cashBillType changes
    useEffect(() => {
        // console.log("Cash Bill Type Changed to:", cashBillType);
        // console.log("Current form values:", getValues());
    }, [cashBillType, getValues]);

    // Add effect to monitor form reset
    useEffect(() => {
        // console.log("Form reset triggered");
        // console.log("New form values:", getValues());
    }, [getValues]);

    // Column body templates
    const productBodyTemplate = (
        rowData: CashBillItem,
        { rowIndex }: { rowIndex: number }
    ) => {
        // Find the correct index in the cashBillItemsFields array using the item's unique id
        const actualIndex = cashBillItemsFields.findIndex(
            (field) => field.id === rowData.id
        );
        const correctIndex = actualIndex >= 0 ? actualIndex : rowIndex;

        // Get currently selected products to prevent duplicates
        const currentItems = getValues("cashBillItems");
        const selectedProductCodes = currentItems
            .map((item, index) =>
                index !== correctIndex ? item.product?.productcode : null
            )
            .filter((code) => code !== null);

        // Filter out already selected products
        const availableProducts = products.filter(
            (product) => !selectedProductCodes.includes(product.productcode)
        );

        return (
            <Controller
                name={`cashBillItems.${correctIndex}.product`}
                control={control}
                rules={{
                    required: "Product is required",
                }}
                render={({ field }) => (
                    <Dropdown
                        {...field}
                        options={availableProducts}
                        optionLabel="productdesc"
                        placeholder="Select Product"
                        className="w-full"
                        filter
                        onChange={(e) => {
                            field.onChange(e.value);
                            if (e.value) {
                                // Set HSN/SAC code
                                const hsnCode = e.value.gscode || "";
                                setValue(
                                    `cashBillItems.${correctIndex}.hsn`,
                                    hsnCode
                                );

                                // Set GST rate based on conditional logic
                                let gstRate = e.value.tax?.IGST || 0;

                                // Apply GST validation rules
                                if (cashBillType === "sales") {
                                    // For sales: if GSTIN is null, GST should be 0
                                    if (!orgGstin) {
                                        gstRate = 0;
                                    }
                                } else {
                                    // For purchase: if applyGst checkbox is unchecked, GST should be 0
                                    if (!applyGst) {
                                        gstRate = 0;
                                    }
                                }

                                setValue(
                                    `cashBillItems.${correctIndex}.gstRate`,
                                    gstRate
                                );

                                // Check if it's a service (gsflag === 19) and set quantity to 1
                                if (e.value.gsflag === 19) {
                                    setValue(
                                        `cashBillItems.${correctIndex}.qty`,
                                        1
                                    );
                                    // Clear free quantity for services
                                    setValue(
                                        `cashBillItems.${correctIndex}.freeQty`,
                                        0
                                    );
                                } else {
                                    setValue(
                                        `cashBillItems.${correctIndex}.qty`,
                                        0
                                    );
                                }

                                // Trigger recalculation
                                setTimeout(() => {
                                    calculateTotals();
                                }, 0);
                            } else {
                                // Clear values when product is deselected
                                setValue(
                                    `cashBillItems.${correctIndex}.hsn`,
                                    ""
                                );
                                setValue(
                                    `cashBillItems.${correctIndex}.gstRate`,
                                    0
                                );
                                setValue(
                                    `cashBillItems.${correctIndex}.qty`,
                                    0
                                );
                                setValue(
                                    `cashBillItems.${correctIndex}.freeQty`,
                                    0
                                );

                                // Trigger recalculation
                                setTimeout(() => {
                                    calculateTotals();
                                }, 0);
                            }
                        }}
                    />
                )}
            />
        );
    };

    const hsnBodyTemplate = (
        rowData: CashBillItem,
        { rowIndex }: { rowIndex: number }
    ) => (
        <InputText
            value={rowData.hsn}
            className="w-full"
            readOnly
            disabled
            style={{ backgroundColor: "#f5f5f5" }}
        />
    );

    const qtyBodyTemplate = (
        rowData: CashBillItem,
        { rowIndex }: { rowIndex: number }
    ) => {
        // Find the correct index in the cashBillItemsFields array using the item's unique id
        const actualIndex = cashBillItemsFields.findIndex(
            (field) => field.id === rowData.id
        );
        const correctIndex = actualIndex >= 0 ? actualIndex : rowIndex;

        // Check if it's a service (gsflag === 19)
        const isService = rowData.product?.gsflag === 19;

        return (
            <Controller
                name={`cashBillItems.${correctIndex}.qty`}
                control={control}
                rules={{
                    required: isService
                        ? false
                        : "Please enter the quantity for products.",
                    validate: (value) => {
                        // For products (gsflag === 7), quantity is required and must be > 0
                        if (rowData.product?.gsflag === 7) {
                            if (!value || value <= 0) {
                                return "Quantity must be a positive number with up to 3 decimal places";
                            }
                        }
                        // Validate decimal places (3 decimal places like in AddInvoice)
                        if (
                            value &&
                            !/^\d*\.?\d{0,3}$/.test(value.toString())
                        ) {
                            return "Quantity must have up to 3 decimal places";
                        }
                        return true;
                    },
                }}
                render={({ field, fieldState }) => (
                    <InputNumber
                        value={field.value}
                        onValueChange={(e) => {
                            field.onChange(e.value);
                            // Trigger validation for real-time error display
                            setTimeout(() => {
                                trigger(`cashBillItems.${correctIndex}.qty`);
                            }, 0);
                            // Trigger recalculation (don't change amount, only recalculate totals)
                            setTimeout(() => {
                                calculateTotals();
                            }, 0);
                        }}
                        onBlur={() => {
                            // Trigger validation when field loses focus
                            trigger(`cashBillItems.${correctIndex}.qty`);
                        }}
                        placeholder="0.000"
                        className={`w-full ${
                            fieldState.invalid ? "p-invalid" : ""
                            // ""
                        }`}
                        inputStyle={{
                            textAlign: "right",
                            backgroundColor: isService ? "#f5f5f5" : undefined,
                        }}
                        min={0}
                        mode="decimal"
                        minFractionDigits={3}
                        maxFractionDigits={3}
                        useGrouping={false}
                        readOnly={isService}
                        disabled={isService}
                    />
                )}
            />
        );
    };

    const freeQtyBodyTemplate = (
        rowData: CashBillItem,
        { rowIndex }: { rowIndex: number }
    ) => {
        // Find the correct index in the cashBillItemsFields array using the item's unique id
        const actualIndex = cashBillItemsFields.findIndex(
            (field) => field.id === rowData.id
        );
        const correctIndex = actualIndex >= 0 ? actualIndex : rowIndex;

        // Check if it's a service (gsflag === 19) - services should not allow free quantity
        const isService = rowData.product?.gsflag === 19;

        return (
            <Controller
                name={`cashBillItems.${correctIndex}.freeQty`}
                control={control}
                rules={{
                    validate: (value) => {
                        // Services should not allow free quantity
                        if (isService && value && value > 0) {
                            return "Free quantity is not allowed for services";
                        }

                        if (!value || value <= 0) return true; // Allow empty or zero free quantity

                        // Allow free quantity to be greater than regular quantity

                        // Validate up to 3 decimal places (same as AddInvoice)
                        if (!/^\d*\.?\d{0,3}$/.test(value.toString())) {
                            return "Free quantity must have up to 3 decimal places";
                        }

                        return true;
                    },
                }}
                render={({ field, fieldState }) => (
                    <InputNumber
                        value={field.value}
                        onValueChange={(e) => {
                            field.onChange(e.value);
                            // Trigger recalculation when free quantity changes
                            setTimeout(() => {
                                calculateTotals();
                            }, 0);
                        }}
                        placeholder="0.000"
                        className={`w-full ${
                            fieldState.invalid ? "p-invalid" : ""
                        }`}
                        // className="w-full"
                        inputStyle={{
                            textAlign: "right",
                            backgroundColor: isService ? "#f5f5f5" : undefined,
                        }}
                        min={0}
                        mode="decimal"
                        minFractionDigits={3}
                        maxFractionDigits={3}
                        useGrouping={false}
                        readOnly={isService}
                        disabled={isService}
                    />
                )}
            />
        );
    };

    const amountBodyTemplate = (
        rowData: CashBillItem,
        { rowIndex }: { rowIndex: number }
    ) => {
        // Find the correct index in the cashBillItemsFields array using the item's unique id
        const actualIndex = cashBillItemsFields.findIndex(
            (field) => field.id === rowData.id
        );
        const correctIndex = actualIndex >= 0 ? actualIndex : rowIndex;

        return (
            <Controller
                name={`cashBillItems.${correctIndex}.amount`}
                control={control}
                rules={{
                    required: "Please enter the amount.",
                    min: {
                        value: 0.001,
                        message: "Amount must be greater than 0.",
                    },
                    validate: (value) => {
                        if (!value || value <= 0) {
                            return "Amount must be a positive number with up to 3 decimal places";
                        } else if (!/^\d*\.?\d{0,3}$/.test(value.toString())) {
                            return "Amount must have up to 3 decimal places";
                        }
                        return true;
                    },
                }}
                render={({ field, fieldState }) => (
                    <InputNumber
                        value={field.value}
                        onValueChange={(e) => {
                            field.onChange(e.value);
                            // Trigger immediate recalculation
                            setTimeout(() => {
                                calculateTotals();
                            }, 0);
                        }}
                        placeholder="0.00"
                        className={`w-full ${
                            fieldState.invalid ? "p-invalid" : ""
                        }`}
                        inputStyle={{ textAlign: "right" }}
                        min={0}
                        mode="decimal"
                        minFractionDigits={2}
                        maxFractionDigits={2}
                        useGrouping={false}
                    />
                )}
            />
        );
    };

    const discountBodyTemplate = (
        rowData: CashBillItem,
        { rowIndex }: { rowIndex: number }
    ) => {
        // Find the correct index in the cashBillItemsFields array using the item's unique id
        const actualIndex = cashBillItemsFields.findIndex(
            (field) => field.id === rowData.id
        );
        const correctIndex = actualIndex >= 0 ? actualIndex : rowIndex;

        return (
            <div className="flex items-center gap-2">
                <Controller
                    name={`cashBillItems.${correctIndex}.discount`}
                    control={control}
                    rules={{
                        validate: (value) => {
                            if (!value || value <= 0) return true; // Allow empty or zero discount

                            // Validate decimal places based on discount type
                            if (discountType === "percentage") {
                                if (value > 100) {
                                    return "Percentage discount cannot exceed 100%";
                                }
                                if (!/^\d*\.?\d{0,2}$/.test(value.toString())) {
                                    return "Percentage discount must have up to 2 decimal places";
                                }
                            } else {
                                if (!/^\d*\.?\d{0,3}$/.test(value.toString())) {
                                    return "Amount discount must have up to 3 decimal places";
                                }
                            }
                            return true;
                        },
                    }}
                    render={({ field, fieldState }) => (
                        <InputNumber
                            value={field.value}
                            onValueChange={(e) => {
                                const value = e.value || 0.0;

                                // Prevent percentage discount above 100%
                                if (
                                    discountType === "percentage" &&
                                    value > 100
                                ) {
                                    toast.current?.show({
                                        severity: "warn",
                                        summary: "Invalid Discount",
                                        detail: "Percentage discount cannot exceed 100%",
                                        life: 3000,
                                    });
                                    // Don't update the field value, keep the previous valid value
                                    return;
                                }

                                field.onChange(e.value);
                                // Trigger immediate recalculation
                                setTimeout(() => {
                                    calculateTotals();
                                }, 0);
                            }}
                            onKeyDown={(e) => {
                                // Prevent typing values that would exceed 100% for percentage discounts
                                if (discountType === "percentage") {
                                    const key = e.key;

                                    // Allow control keys (backspace, delete, arrow keys, etc.)
                                    if (
                                        e.ctrlKey ||
                                        e.metaKey ||
                                        key.length > 1
                                    ) {
                                        return;
                                    }

                                    // Check if the new value would exceed 100
                                    if (/[0-9]/.test(key)) {
                                        const input =
                                            e.currentTarget as HTMLInputElement;
                                        const selectionStart =
                                            input.selectionStart || 0;
                                        const selectionEnd =
                                            input.selectionEnd || 0;
                                        const currentText = input.value;

                                        // Simulate the new value after key press
                                        const newText =
                                            currentText.slice(
                                                0,
                                                selectionStart
                                            ) +
                                            key +
                                            currentText.slice(selectionEnd);

                                        // Remove any non-numeric characters except decimal point for validation
                                        const numericValue = parseFloat(
                                            newText.replace(/[^0-9.]/g, "")
                                        );

                                        if (
                                            !isNaN(numericValue) &&
                                            numericValue > 100
                                        ) {
                                            e.preventDefault();
                                            toast.current?.show({
                                                severity: "warn",
                                                summary: "Invalid Input",
                                                detail: "Percentage discount cannot exceed 100%",
                                                life: 2000,
                                            });
                                        }
                                    }
                                }
                            }}
                            onPaste={(e) => {
                                // Prevent pasting values that would exceed 100% for percentage discounts
                                if (discountType === "percentage") {
                                    const pastedData =
                                        e.clipboardData.getData("text");
                                    const numericValue = parseFloat(
                                        pastedData.replace(/[^0-9.]/g, "")
                                    );

                                    if (
                                        !isNaN(numericValue) &&
                                        numericValue > 100
                                    ) {
                                        e.preventDefault();
                                        toast.current?.show({
                                            severity: "warn",
                                            summary: "Invalid Paste",
                                            detail: "Percentage discount cannot exceed 100%",
                                            life: 2000,
                                        });
                                    }
                                }
                            }}
                            placeholder={
                                discountType === "amount" ? "0.00" : "0.00"
                            }
                            className={`w-20 ${
                                fieldState.invalid ? "p-invalid" : ""
                            }`}
                            min={0}
                            max={
                                discountType === "percentage" ? 100 : undefined
                            }
                            mode="decimal"
                            minFractionDigits={2}
                            maxFractionDigits={2}
                            suffix={
                                discountType === "percentage" ? "%" : undefined
                            }
                            inputStyle={{ textAlign: "right" }}
                            useGrouping={false}
                        />
                    )}
                />
                <span className="text-sm text-gray-500">
                    {discountType === "amount" ? "Amt" : "%"}
                </span>
            </div>
        );
    };

    const discountHeader = (
        <div className="flex items-center gap-2">
            <span>Discount</span>
            <Dropdown
                value={discountType}
                options={[
                    { label: "Amt", value: "amount" },
                    { label: "%", value: "percentage" },
                ]}
                onChange={(e) => {
                    setDiscountType(e.value);
                    // Clear all discount values when switching types
                    cashBillItemsFields.forEach((_, index) => {
                        setValue(`cashBillItems.${index}.discount`, 0);
                    });
                    setTimeout(() => calculateTotals(), 0);
                }}
                placeholder="Select Discount Type"
                className="p-dropdown-sm w-24 smaller-dropdown"
            />
        </div>
    );

    const taxableValueBodyTemplate = (rowData: CashBillItem) => {
        // const qty = rowData.qty || 0;
        // const amount = rowData.amount || 0;
        // const baseValue = qty * amount;

        return (
            <div className="w-full">
                <InputText
                    value={(rowData.taxableValue || 0).toFixed(2)}
                    disabled
                    className="w-full"
                    style={{ textAlign: "right" }}
                    placeholder="0.00"
                />
                {/* {qty > 0 && amount > 0 && (
                    <small className="text-xs text-gray-500 block text-right mt-1">
                        {qty} × {amount} = {baseValue.toFixed(2)}
                    </small>
                )} */}
            </div>
        );
    };

    const gstRateBodyTemplate = (rowData: CashBillItem) => (
        <InputText
            value={`${(rowData.gstRate || 0).toFixed(2)}%`}
            disabled
            className="w-full"
            style={{ textAlign: "right" }}
        />
    );

    const gstAmountBodyTemplate = (rowData: CashBillItem) => (
        <InputText
            value={(rowData.gstAmount || 0).toFixed(2)}
            disabled
            className="w-full"
            style={{ textAlign: "right" }}
            placeholder="0.00"
        />
    );

    const totalBodyTemplate = (rowData: CashBillItem) => (
        <InputText
            value={(rowData.total || 0).toFixed(2)}
            disabled
            className="w-full font-semibold"
            style={{ textAlign: "right" }}
            placeholder="0.00"
        />
    );

    const actionBodyTemplate = (
        rowData: CashBillItem,
        { rowIndex }: { rowIndex: number }
    ) => {
        if (cashBillItemsFields.length === 1) {
            return null;
        }

        // Find the correct index in the cashBillItemsFields array using the item's unique id
        const actualIndex = cashBillItemsFields.findIndex(
            (field) => field.id === rowData.id
        );

        return (
            <Button
                type="button"
                icon="pi pi-trash"
                className="p-button-danger p-button-sm"
                onClick={() =>
                    removeItem(actualIndex >= 0 ? actualIndex : rowIndex)
                }
            />
        );
    };

    // MobileCashBillItem component
    const MobileCashBillItem: React.FC<{
        item: CashBillItem;
        index: number;
    }> = ({ item, index }) => {
        const {
            formState: { errors },
        } = useFormContext<
            CashBillFormValues & { cashBillItems: CashBillItem[] }
        >();
        const itemErrors =
            errors.cashBillItems && Array.isArray(errors.cashBillItems)
                ? errors.cashBillItems[index]
                : undefined;

        return (
            <Card key={item.id} className="mb-4">
                <div className="grid grid-cols-1 gap-4 mx-auto item-center justify-content-center">
                    <div className="w-full">
                        <label className="block text-sm font-medium mb-2">
                            Product/Service{" "}
                            <span className="text-red-500">*</span>
                        </label>
                        {productBodyTemplate(item, { rowIndex: index })}
                        {itemErrors?.product && (
                            <small className="p-error">
                                {itemErrors.product.message}
                            </small>
                        )}
                    </div>
                    <div className="w-full">
                        <label className="block text-sm font-medium mb-2">
                            HSN/SAC
                        </label>
                        {hsnBodyTemplate(item, { rowIndex: index })}
                    </div>
                    <div className="w-full">
                        <label className="block text-sm font-medium mb-2">
                            Qty <span className="text-red-500">*</span>
                        </label>
                        {qtyBodyTemplate(item, { rowIndex: index })}
                        {itemErrors?.qty && (
                            <small className="p-error">
                                {itemErrors.qty.message}
                            </small>
                        )}
                    </div>
                    <div className="w-full">
                        <label className="block text-sm font-medium mb-2">
                            Free Qty
                        </label>
                        {freeQtyBodyTemplate(item, { rowIndex: index })}
                    </div>
                    <div className="w-full">
                        <label className="block text-sm font-medium mb-2">
                            Amount <span className="text-red-500">*</span>
                        </label>
                        {amountBodyTemplate(item, { rowIndex: index })}
                        {itemErrors?.amount && (
                            <small className="p-error">
                                {itemErrors.amount.message}
                            </small>
                        )}
                    </div>
                    <div className="w-full">
                        <label className="block text-sm font-medium mb-2">
                            Discount
                        </label>

                        {discountBodyTemplate(item, { rowIndex: index })}
                    </div>
                    <div className="w-full">
                        <label className="block text-sm font-medium mb-2">
                            Taxable Value
                        </label>
                        {taxableValueBodyTemplate(item)}
                    </div>
                    <div className="w-full">
                        <label className="block text-sm font-medium mb-2">
                            GST Rate
                        </label>
                        {gstRateBodyTemplate(item)}
                    </div>
                    <div className="w-full">
                        <label className="block text-sm font-medium mb-2">
                            GST Amount
                        </label>
                        {gstAmountBodyTemplate(item)}
                    </div>
                    <div className="w-full">
                        <label className="block text-sm font-medium mb-2">
                            Total
                        </label>
                        {totalBodyTemplate(item)}
                    </div>
                    <div className="flex justify-center">
                        {actionBodyTemplate(item, { rowIndex: index })}
                    </div>
                </div>
            </Card>
        );
    };

    return (
        <OlkProvider>
            <Toast ref={toast} />
            <Card className="p-4">
                <div className="flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2 className="text-xl md:text-2xl font-semibold">
                            {title}
                        </h2>
                        {/* Cash Bill Type Selection */}
                        {/* <Card className=""> */}
                        <div className="flex align-items-center gap-4">
                            <div className="flex align-items-center">
                                <RadioButton
                                    inputId="sales"
                                    name="cashBillType"
                                    value="sales"
                                    onChange={(e) => {
                                        // console.log("Sales radio clicked");
                                        handleCashBillTypeChange("sales");
                                    }}
                                    checked={cashBillType === "sales"}
                                />
                                <label htmlFor="sales" className="ml-2">
                                    Sales
                                </label>
                            </div>
                            <div className="flex align-items-center">
                                <RadioButton
                                    inputId="purchase"
                                    name="cashBillType"
                                    value="purchase"
                                    onChange={(e) => {
                                        // console.log(
                                        //     "Purchase radio clicked"
                                        // );
                                        handleCashBillTypeChange("purchase");
                                    }}
                                    checked={cashBillType === "purchase"}
                                />
                                <label htmlFor="purchase" className="ml-2">
                                    Purchase
                                </label>
                            </div>
                        </div>
                    </div>

                    {/* </Card> */}
                    <div className="flex gap-2">
                        <Button
                            type="button"
                            label="View"
                            size="small"
                            className="custom-button custom-button-raised outlined"
                            onClick={handleViewCashBills}
                        />
                    </div>
                </div>
                <Divider />

                <FormProvider {...methods}>
                    <form
                        onSubmit={handleSubmit(onSubmit)}
                        className="p-fluid grid gap-4"
                    >
                        {/* Cash Bill No and Date */}
                        <Card className="w-full">
                            <div className="formgrid grid md:col-6 w-full">
                                <div className="field col-12 md:col-3">
                                    <label
                                        htmlFor="cashBillNo"
                                        className="mb-2"
                                    >
                                        Cash Bill No :{" "}
                                        <span className="text-red-500">*</span>
                                    </label>
                                    <Controller
                                        name="cashBillNo"
                                        control={control}
                                        rules={{
                                            validate: {
                                                format: validateCashBillNumberFormat,
                                                uniqueness:
                                                    validateCashBillNumber,
                                            },
                                        }}
                                        render={({ field }) => (
                                            <InputText
                                                id="cashBillNo"
                                                {...field}
                                                placeholder="Enter cash bill number"
                                                className={`w-full ${
                                                    errors.cashBillNo ||
                                                    cashBillNoValidating
                                                        ? "p-invalid"
                                                        : ""
                                                }`}
                                                onKeyDown={
                                                    handleCashBillNumberKeyDown
                                                }
                                                onChange={(e) => {
                                                    const input =
                                                        e.target.value;
                                                    const currentValue =
                                                        field.value || "";

                                                    // Filter the input to only allow valid characters
                                                    const filteredInput =
                                                        filterCashBillNumberInput(
                                                            input,
                                                            currentValue
                                                        );

                                                    // Update field value with filtered input
                                                    field.onChange(
                                                        filteredInput
                                                    );

                                                    // Trigger real-time Zod format validation
                                                    if (
                                                        filteredInput &&
                                                        filteredInput.length > 0
                                                    ) {
                                                        // Trigger form validation which will use our Zod schema
                                                        trigger("cashBillNo");

                                                        // Also trigger uniqueness validation (debounced)
                                                        debouncedValidateCashBillNumber(
                                                            filteredInput
                                                        );
                                                    }
                                                }}
                                                onPaste={(e) => {
                                                    e.preventDefault();
                                                    const pasted =
                                                        e.clipboardData.getData(
                                                            "text"
                                                        );
                                                    const currentValue =
                                                        field.value || "";

                                                    // Filter the pasted content
                                                    const filteredPasted =
                                                        filterCashBillNumberInput(
                                                            pasted,
                                                            currentValue
                                                        );

                                                    // Update field value with filtered content
                                                    field.onChange(
                                                        filteredPasted
                                                    );

                                                    // Trigger real-time validation for pasted content
                                                    if (
                                                        filteredPasted &&
                                                        filteredPasted.length >
                                                            0
                                                    ) {
                                                        // Trigger form validation which will use our Zod schema
                                                        trigger("cashBillNo");

                                                        // Also trigger uniqueness validation
                                                        validateCashBillNumber(
                                                            filteredPasted
                                                        );
                                                    }
                                                }}
                                                onBlur={field.onBlur}
                                            />
                                        )}
                                    />
                                    {errors.cashBillNo && (
                                        <small className="p-error">
                                            {errors.cashBillNo.message}
                                        </small>
                                    )}
                                </div>

                                <div className="field col-12 md:col-3">
                                    <label
                                        htmlFor="cashBillDate"
                                        className="mb-2"
                                    >
                                        Date :{" "}
                                        <span className="text-red-500">*</span>
                                    </label>
                                    <Controller
                                        name="cashBillDate"
                                        control={control}
                                        rules={{
                                            required: "Please select a date.",
                                        }}
                                        render={({ field }) => (
                                            <Calendar
                                                id="cashBillDate"
                                                {...field}
                                                dateFormat="dd-mm-yy"
                                                className={`w-full ${
                                                    errors.cashBillDate
                                                        ? "p-invalid"
                                                        : ""
                                                }`}
                                                maxDate={new Date()}
                                                minDate={
                                                    yearstart
                                                        ? new Date(yearstart)
                                                        : undefined
                                                }
                                                placeholder="Select Date"
                                            />
                                        )}
                                    />
                                    {errors.cashBillDate && (
                                        <small className="p-error">
                                            {errors.cashBillDate.message}
                                        </small>
                                    )}
                                </div>

                                {/* GST Information and Checkbox */}
                                <div className="field col-12 md:col-6">
                                    {cashBillType === "purchase" && (
                                        <div className="field mt-3">
                                            <div className="flex align-items-center">
                                                <Checkbox
                                                    inputId="applyGst"
                                                    checked={applyGst}
                                                    onChange={(e) => {
                                                        setApplyGst(
                                                            e.checked || false
                                                        );
                                                        // Trigger recalculation when GST checkbox changes
                                                        setTimeout(() => {
                                                            calculateTotals();
                                                        }, 0);
                                                    }}
                                                />
                                                <label
                                                    htmlFor="applyGst"
                                                    className="ml-2"
                                                >
                                                    Apply GST
                                                </label>
                                            </div>
                                            <small className="text-gray-600 block mt-1">
                                                Uncheck to set GST rate to 0%
                                                for all items
                                            </small>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </Card>

                        {/* Product/Service Table */}
                        <div className="overflow-x-auto">
                            <Card className="mb-4">
                                <h3 className="text-lg font-semibold mb-3">
                                    Products/Services
                                </h3>
                                {productLoading ? (
                                    <div className="flex justify-center items-center h-20 m-auto">
                                        <ProgressSpinner />
                                    </div>
                                ) : (
                                    <div className="flex flex-col gap-4 max-w-full overflow-x-auto">
                                        {/* Mobile View */}
                                        <div className="block md:hidden">
                                            <div className="flex items-center gap-2 mb-4">
                                                <label className="font-medium">
                                                    Discount Type:
                                                </label>
                                                <Dropdown
                                                    value={discountType}
                                                    options={[
                                                        {
                                                            label: "Amt",
                                                            value: "amount",
                                                        },
                                                        {
                                                            label: "%",
                                                            value: "percentage",
                                                        },
                                                    ]}
                                                    onChange={(e) => {
                                                        setDiscountType(
                                                            e.value
                                                        );
                                                        // Clear all discount values when switching types
                                                        cashBillItemsFields.forEach(
                                                            (_, index) => {
                                                                setValue(
                                                                    `cashBillItems.${index}.discount`,
                                                                    0
                                                                );
                                                            }
                                                        );
                                                        setTimeout(
                                                            () =>
                                                                calculateTotals(),
                                                            0
                                                        );
                                                    }}
                                                    placeholder="Select Discount Type"
                                                    className="p-dropdown-sm w-24 smaller-dropdown"
                                                />
                                            </div>
                                            {cashBillItemsFields.map(
                                                (item, index) => (
                                                    <MobileCashBillItem
                                                        key={item.id}
                                                        item={item}
                                                        index={index}
                                                    />
                                                )
                                            )}
                                            <div className="flex flex-col gap-4 mt-4 invoice-footer">
                                                <div className="flex justify-end gap-2">
                                                    <Button
                                                        type="button"
                                                        label="Add Product"
                                                        icon="pi pi-plus"
                                                        size="small"
                                                        className="p-button-primary p-button-sm w-auto"
                                                        onClick={addItem}
                                                    />
                                                </div>
                                                <div className="flex justify-end flex-wrap gap-4 text-center font-bold">
                                                    <div>
                                                        Taxable Value: ₹
                                                        {totals.totalTaxable.toFixed(
                                                            2
                                                        )}
                                                    </div>
                                                    <div>
                                                        GST Amount: ₹
                                                        {totals.totalGST.toFixed(
                                                            2
                                                        )}
                                                    </div>
                                                    <div>
                                                        Total: ₹
                                                        {totals.grandTotal.toFixed(
                                                            2
                                                        )}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        {/* Desktop View */}
                                        <div className="hidden md:block">
                                            <DataTable
                                                value={watchedItems}
                                                dataKey="id"
                                                breakpoint="960px"
                                                className="p-datatable-sm w-full max-w-full"
                                                responsiveLayout="scroll"
                                                scrollable
                                                scrollHeight="auto"
                                                footer={
                                                    <div className="invoice-footer text-center font-bold">
                                                        <div className="button-container flex justify-start gap-2">
                                                            <Button
                                                                type="button"
                                                                label="Add Product"
                                                                icon="pi pi-plus"
                                                                size="small"
                                                                className="p-button-primary p-button-sm w-auto"
                                                                onClick={
                                                                    addItem
                                                                }
                                                            />
                                                        </div>
                                                        <div className="flex justify-end flex-wrap gap-4">
                                                            <div>
                                                                Taxable Value: ₹
                                                                {totals.totalTaxable.toFixed(
                                                                    2
                                                                )}
                                                            </div>
                                                            <div>
                                                                GST Amount: ₹
                                                                {totals.totalGST.toFixed(
                                                                    2
                                                                )}
                                                            </div>
                                                            <div>
                                                                Total: ₹
                                                                {totals.grandTotal.toFixed(
                                                                    2
                                                                )}
                                                            </div>
                                                        </div>
                                                    </div>
                                                }
                                            >
                                                <Column
                                                    field="product"
                                                    header={
                                                        <>
                                                            Product/Service{" "}
                                                            <span className="text-red-500">
                                                                *
                                                            </span>
                                                        </>
                                                    }
                                                    body={productBodyTemplate}
                                                    style={{
                                                        minWidth: "200px",
                                                    }}
                                                    headerStyle={{
                                                        backgroundColor:
                                                            "#bfdbfe",
                                                    }}
                                                />
                                                <Column
                                                    field="hsn"
                                                    header="HSN/SAC"
                                                    body={hsnBodyTemplate}
                                                    style={{
                                                        minWidth: "100px",
                                                    }}
                                                    headerStyle={{
                                                        backgroundColor:
                                                            "#bfdbfe",
                                                    }}
                                                />
                                                <Column
                                                    field="qty"
                                                    header={
                                                        <>
                                                            Qty{" "}
                                                            <span className="text-red-500">
                                                                *
                                                            </span>
                                                        </>
                                                    }
                                                    body={qtyBodyTemplate}
                                                    style={{
                                                        minWidth: "100px",
                                                    }}
                                                    headerStyle={{
                                                        backgroundColor:
                                                            "#bfdbfe",
                                                    }}
                                                />
                                                <Column
                                                    field="freeQty"
                                                    header="Free Qty"
                                                    body={freeQtyBodyTemplate}
                                                    style={{
                                                        minWidth: "100px",
                                                    }}
                                                    headerStyle={{
                                                        backgroundColor:
                                                            "#bfdbfe",
                                                    }}
                                                />
                                                <Column
                                                    field="amount"
                                                    header={
                                                        <>
                                                            Amount{" "}
                                                            <span className="text-red-500">
                                                                *
                                                            </span>
                                                        </>
                                                    }
                                                    body={amountBodyTemplate}
                                                    style={{
                                                        minWidth: "120px",
                                                    }}
                                                    headerStyle={{
                                                        backgroundColor:
                                                            "#bfdbfe",
                                                    }}
                                                />
                                                <Column
                                                    field="discount"
                                                    header={discountHeader}
                                                    body={discountBodyTemplate}
                                                    style={{
                                                        minWidth: "180px",
                                                    }}
                                                    headerStyle={{
                                                        backgroundColor:
                                                            "#bfdbfe",
                                                    }}
                                                />
                                                <Column
                                                    field="taxableValue"
                                                    header="Taxable Value"
                                                    body={
                                                        taxableValueBodyTemplate
                                                    }
                                                    style={{
                                                        minWidth: "120px",
                                                    }}
                                                    headerStyle={{
                                                        backgroundColor:
                                                            "#bfdbfe",
                                                    }}
                                                />
                                                <Column
                                                    field="gstRate"
                                                    header="GST Rate"
                                                    body={gstRateBodyTemplate}
                                                    style={{
                                                        minWidth: "100px",
                                                    }}
                                                    headerStyle={{
                                                        backgroundColor:
                                                            "#bfdbfe",
                                                    }}
                                                />
                                                <Column
                                                    field="gstAmount"
                                                    header="GST Amount"
                                                    body={gstAmountBodyTemplate}
                                                    style={{
                                                        minWidth: "120px",
                                                    }}
                                                    headerStyle={{
                                                        backgroundColor:
                                                            "#bfdbfe",
                                                    }}
                                                />
                                                <Column
                                                    field="total"
                                                    header="Total"
                                                    body={totalBodyTemplate}
                                                    style={{
                                                        minWidth: "120px",
                                                    }}
                                                    headerStyle={{
                                                        backgroundColor:
                                                            "#bfdbfe",
                                                    }}
                                                />
                                                <Column
                                                    header="Action"
                                                    body={actionBodyTemplate}
                                                    style={{ minWidth: "80px" }}
                                                    headerStyle={{
                                                        backgroundColor:
                                                            "#bfdbfe",
                                                    }}
                                                />
                                            </DataTable>
                                        </div>
                                    </div>
                                )}
                            </Card>
                        </div>

                        {/* Additional Sections: Payment Details and Cash Bill Summary */}
                        <div className="grid card-grid-container mt-4 md:p-2 gap-3">
                            {/* Payment Details */}
                            <Card className="col-12 md:col-4 my-3">
                                <div className="field">
                                    <label
                                        htmlFor="paymentMethod"
                                        className="mb-2"
                                    >
                                        Payment Method :{" "}
                                        <span className="text-red-500">*</span>
                                    </label>
                                    <Controller
                                        name="paymentMethod"
                                        control={control}
                                        rules={{
                                            required:
                                                "Please select a payment method.",
                                        }}
                                        render={({ field }) => (
                                            <Dropdown
                                                id="paymentMethod"
                                                {...field}
                                                options={paymentOptions}
                                                placeholder="Select Payment Method"
                                                className="w-full"
                                            />
                                        )}
                                    />
                                </div>

                                {paymentMethod === "Cash" && (
                                    <div className="field mt-3">
                                        <label style={{ color: "green" }}>
                                            Cash Received.
                                        </label>
                                    </div>
                                )}

                                {paymentMethod === "Bank" && (
                                    <div className="field mt-3">
                                        <label style={{ color: "green" }}>
                                            Received Bank Transfer.
                                        </label>
                                    </div>
                                )}

                                {paymentMethod === "Online" && (
                                    <div className="field mt-3">
                                        <label style={{ color: "green" }}>
                                            Received Online Payment.
                                        </label>
                                    </div>
                                )}
                            </Card>

                            {/* Cash Bill Summary */}
                            <Card className="col-12 md:col-4 my-3">
                                <h3 className="text-lg font-semibold mb-3">
                                    Cash Bill Summary
                                </h3>
                                <div className="invoice-summary-container">
                                    <div className="invoice-summary-row">
                                        <span className="w-32 text-left">
                                            Total Taxable Amount :
                                        </span>
                                        <span className="flex-1 text-right">
                                            ₹
                                            {formatIndianCurrency(
                                                totals.totalTaxable.toFixed(2)
                                            )}
                                        </span>
                                    </div>
                                    <div className="invoice-summary-row">
                                        <span className="w-32 text-left">
                                            Total CGST :
                                        </span>
                                        <span className="flex-1 text-right">
                                            ₹
                                            {formatIndianCurrency(
                                                (totals.totalGST / 2).toFixed(2)
                                            )}
                                        </span>
                                    </div>
                                    <div className="invoice-summary-row">
                                        <span className="w-32 text-left">
                                            Total SGST :
                                        </span>
                                        <span className="flex-1 text-right">
                                            ₹
                                            {formatIndianCurrency(
                                                (totals.totalGST / 2).toFixed(2)
                                            )}
                                        </span>
                                    </div>
                                    <div className="invoice-summary-row">
                                        <span className="w-32 text-left">
                                            Total Discount :
                                        </span>
                                        <span className="flex-1 text-right">
                                            ₹
                                            {formatIndianCurrency(
                                                totals.totalDiscount.toFixed(2)
                                            )}
                                        </span>
                                    </div>
                                    <div className="invoice-summary-row">
                                        <span className="w-32 text-left">
                                            Total Cash Bill Value :
                                        </span>
                                        <span className="flex-1 text-right">
                                            ₹
                                            {formatIndianCurrency(
                                                totals.grandTotal.toFixed(2)
                                            )}
                                        </span>
                                    </div>
                                    <div className="invoice-summary-row">
                                        <span className="w-32 text-left">
                                            Amount Paid :
                                        </span>
                                        <span className="flex-1 text-right text-green-600 font-semibold">
                                            ₹
                                            {formatIndianCurrency(
                                                totals.grandTotal.toFixed(2)
                                            )}
                                        </span>
                                    </div>
                                    <div className="invoice-summary-row">
                                        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-2 w-full">
                                            <span className="font-bold text-gray-700 whitespace-nowrap">
                                                Amount in Words:
                                            </span>
                                            <span className="flex-1 text-left sm:text-right font-medium text-gray-900 break-words leading-relaxed sm:max-w-xs">
                                                {amountInWords}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </Card>
                        </div>
                        <div className="w-full">
                            {/* Narration */}
                            <Card className="col-12 md:col-8 form.footer">
                                <div className="field">
                                    <label htmlFor="narration" className="mb-2">
                                        Narration
                                    </label>
                                    <Controller
                                        name="narration"
                                        control={control}
                                        render={({ field }) => (
                                            <InputText
                                                id="narration"
                                                {...field}
                                                placeholder="Add narration"
                                                className="w-full"
                                            />
                                        )}
                                    />
                                </div>
                            </Card>
                            {/* Footer Buttons */}
                            <div className="flex gap-2 justify-start mt-4 w-fit">
                                <Button
                                    label="Reset"
                                    icon="pi pi-undo"
                                    className="p-button-secondary"
                                    type="button"
                                    onClick={() => reset()}
                                />
                                <Button
                                    label="Save"
                                    icon="pi pi-save"
                                    className="p-button-success"
                                    type="submit"
                                    loading={loading}
                                />
                            </div>
                        </div>
                    </form>
                </FormProvider>
            </Card>
        </OlkProvider>
    );
};

export default CashBillForm;
